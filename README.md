# 储能材料预测模型 - 重构版本

本项目将原始的单文件代码重构为模块化的结构，提高了代码的可维护性和可扩展性。

## 文件结构

```
├── data_processing.py      # 数据处理、特征构建、数据集划分
├── model_selection.py      # 模型选择、超参数优化
├── model_training.py       # 模型训练、可视化
├── main.py                # 主程序入口
├── energy_storage_predictor.py  # 原始单文件版本
└── README.md              # 说明文档
```

## 模块说明

### 1. data_processing.py
负责数据处理相关功能：
- **DataProcessor类**: 
  - `load_data()`: 加载和合并Excel数据文件
  - `extract_features()`: 提取特征（电场、门捷列夫数、混合熵等）
  - `calculate_mixing_entropy()`: 计算混合熵
  - `split_data_by_group()`: 按组分割数据，避免数据泄露
  - `prepare_data()`: 完整的数据准备流程

### 2. model_selection.py
负责模型选择和超参数优化：
- **ModelSelector类**:
  - `train_models_with_cv()`: 使用交叉验证训练多个模型
  - `_hyperparameter_tuning()`: 超参数网格搜索
  - `evaluate_model()`: 模型性能评估
  - `get_feature_importance()`: 获取特征重要性
  - `compare_models()`: 模型性能比较

### 3. model_training.py
负责模型训练和可视化：
- **ModelTrainer类**:
  - `train_and_evaluate()`: 完整的训练评估流程
  - `plot_results()`: 绘制预测结果图表
  - `plot_model_comparison()`: 绘制模型比较图
  - `print_summary()`: 打印结果总结

## 使用方法

### 基础使用
```bash
# 使用默认参数训练模型
python main.py

# 启用超参数调优（耗时较长）
python main.py --tune

# 只进行数据处理
python main.py --data-only

# 只进行模型比较
python main.py --compare-only
```

### 在代码中使用

#### 1. 单独使用数据处理模块
```python
from data_processing import DataProcessor

processor = DataProcessor()
feature_df, data_splits = processor.prepare_data()
```

#### 2. 单独使用模型选择模块
```python
from model_selection import ModelSelector

selector = ModelSelector()
cv_results, best_model_name, best_model = selector.train_models_with_cv(
    X_train_val, y_train_val, groups_train_val, "目标变量名"
)
```

#### 3. 完整训练流程
```python
from model_training import ModelTrainer

trainer = ModelTrainer(use_hyperparameter_tuning=True)
results = trainer.train_and_evaluate()
trainer.print_summary()
```

## 数据要求

确保以下Excel文件存在于项目目录中：
- `elements_data.xlsx`: 元素门捷列夫数数据
- `A_site_elements_fully_expanded.xlsx`: A位元素数据
- `B_site_elements_fully_expanded.xlsx`: B位元素数据

## 输出文件

运行后会生成以下文件：
- `energy_density_prediction_results.png`: 储能密度预测结果图
- `energy_efficiency_prediction_results.png`: 储能效率预测结果图
- `model_comparison.png`: 模型性能比较图
- `processed_features.csv`: 处理后的特征数据（使用--data-only时）

## 主要改进

1. **模块化设计**: 将功能分离到不同模块，提高代码可维护性
2. **更好的封装**: 每个类负责特定功能，接口清晰
3. **灵活的配置**: 支持命令行参数控制不同运行模式
4. **增强的功能**: 
   - 添加超参数调优功能
   - 增加模型比较可视化
   - 支持特征重要性分析
5. **错误处理**: 更好的异常处理和用户提示

## 依赖包

```
pandas
numpy
scikit-learn
matplotlib
seaborn
openpyxl  # 用于读取Excel文件
```

安装依赖：
```bash
pip install pandas numpy scikit-learn matplotlib seaborn openpyxl
```

## 扩展建议

1. **添加新模型**: 在`model_selection.py`中的`_initialize_models()`方法中添加新模型
2. **新特征工程**: 在`data_processing.py`中的`extract_features()`方法中添加新特征
3. **自定义可视化**: 在`model_training.py`中添加新的绘图方法
4. **配置文件**: 可以添加配置文件来管理参数设置
5. **日志记录**: 添加日志记录功能来跟踪训练过程

## 注意事项

1. 超参数调优会显著增加训练时间
2. 确保数据文件路径正确
3. 建议在有足够内存的环境中运行
4. 可以根据需要调整交叉验证的折数
