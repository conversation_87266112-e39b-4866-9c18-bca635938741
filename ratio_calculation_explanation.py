import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("="*80)
print("差异比率计算方法详细说明")
print("="*80)

# 读取数据
df = pd.read_excel('data2024_copy.xlsx')
df_clean = df.dropna()
df_clean = df_clean.copy()
df_clean['Wrec_normalized'] = 10000 * df_clean['Wrec (J cm-3)'] / (df_clean['E (kV cm-1)'] ** 2)

# 筛选数据
component_test_counts = df_clean.groupby('component').size()
components_with_multiple_tests = component_test_counts[component_test_counts >= 3].index
df_analysis = df_clean[df_clean['component'].isin(components_with_multiple_tests)].copy()

print("\n1. 差异比率的定义:")
print("   差异比率 = 不同成分间变异系数 / 相同成分内平均变异系数")
print("   其中：")
print("   - 变异系数 (CV) = 标准差 / 平均值")
print("   - 用于比较不同量级数据的相对离散程度")

print("\n2. 计算步骤详解:")

# 步骤1: 计算每个成分的平均值
print("\n步骤1: 计算每个成分的平均值")
component_means = df_analysis.groupby('component')['Wrec_normalized'].mean()
print(f"   示例 - 前5个成分的平均值:")
for i, (comp, mean_val) in enumerate(component_means.head().items()):
    print(f"   成分{i+1}: {mean_val:.3f}")

# 步骤2: 计算不同成分间的变异系数
print(f"\n步骤2: 计算不同成分间的变异系数")
print(f"   所有成分平均值的平均值: {component_means.mean():.3f}")
print(f"   所有成分平均值的标准差: {component_means.std():.3f}")
cv_between = component_means.std() / component_means.mean()
print(f"   不同成分间变异系数 = {component_means.std():.3f} / {component_means.mean():.3f} = {cv_between:.4f}")

# 步骤3: 计算每个成分内部的变异系数
print(f"\n步骤3: 计算每个成分内部的变异系数")
within_component_cvs = []
component_examples = []

for i, component in enumerate(components_with_multiple_tests):
    component_data = df_analysis[df_analysis['component'] == component]
    mean_val = component_data['Wrec_normalized'].mean()
    std_val = component_data['Wrec_normalized'].std()
    
    if mean_val != 0:
        cv = std_val / mean_val
        within_component_cvs.append(cv)
        
        # 保存前5个成分作为示例
        if i < 5:
            component_examples.append({
                'component_id': i+1,
                'count': len(component_data),
                'mean': mean_val,
                'std': std_val,
                'cv': cv,
                'values': component_data['Wrec_normalized'].values
            })

print(f"   示例 - 前5个成分的内部变异系数:")
for example in component_examples:
    print(f"   成分{example['component_id']} (n={example['count']}): 均值={example['mean']:.3f}, 标准差={example['std']:.3f}, CV={example['cv']:.3f}")

# 步骤4: 计算相同成分内平均变异系数
print(f"\n步骤4: 计算相同成分内平均变异系数")
avg_within_cv = np.mean(within_component_cvs)
print(f"   所有成分内部变异系数的平均值: {avg_within_cv:.4f}")
print(f"   (基于 {len(within_component_cvs)} 个成分的变异系数)")

# 步骤5: 计算差异比率
print(f"\n步骤5: 计算差异比率")
ratio = cv_between / avg_within_cv
print(f"   差异比率 = {cv_between:.4f} / {avg_within_cv:.4f} = {ratio:.3f}")

print(f"\n3. 差异比率的解释:")
print(f"   - 比率 > 1: 不同成分间差异大于相同成分内差异")
print(f"   - 比率 = 1: 不同成分间差异等于相同成分内差异")
print(f"   - 比率 < 1: 不同成分间差异小于相同成分内差异")
print(f"   本例中比率 = {ratio:.3f} > 1，说明成分是主要影响因素")

# 创建可视化说明
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('差异比率计算过程可视化', fontsize=16, fontweight='bold')

# 1. 不同成分的平均值分布
ax1 = axes[0, 0]
ax1.hist(component_means, bins=30, alpha=0.7, color='#FF6B6B', edgecolor='black')
ax1.axvline(component_means.mean(), color='red', linestyle='--', linewidth=2, 
           label=f'总平均值: {component_means.mean():.3f}')
ax1.axvline(component_means.mean() + component_means.std(), color='orange', linestyle='--', 
           label=f'+1σ: {component_means.mean() + component_means.std():.3f}')
ax1.axvline(component_means.mean() - component_means.std(), color='orange', linestyle='--', 
           label=f'-1σ: {component_means.mean() - component_means.std():.3f}')
ax1.set_title('不同成分平均值的分布', fontsize=12, fontweight='bold')
ax1.set_xlabel('10000*Wrec/(E²) 平均值')
ax1.set_ylabel('成分数量')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 2. 成分内部变异系数分布
ax2 = axes[0, 1]
ax2.hist(within_component_cvs, bins=30, alpha=0.7, color='#4ECDC4', edgecolor='black')
ax2.axvline(avg_within_cv, color='red', linestyle='--', linewidth=2, 
           label=f'平均CV: {avg_within_cv:.3f}')
ax2.set_title('各成分内部变异系数分布', fontsize=12, fontweight='bold')
ax2.set_xlabel('变异系数 (CV)')
ax2.set_ylabel('成分数量')
ax2.legend()
ax2.grid(True, alpha=0.3)

# 3. 典型成分数据展示
ax3 = axes[1, 0]
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
for i, example in enumerate(component_examples):
    values = example['values']
    x_pos = np.full(len(values), i+1) + np.random.normal(0, 0.05, len(values))
    ax3.scatter(x_pos, values, alpha=0.7, color=colors[i], s=50, 
               label=f"成分{example['component_id']} (CV={example['cv']:.3f})")
    ax3.plot([i+0.8, i+1.2], [example['mean'], example['mean']], 
            color='red', linewidth=2)

ax3.set_title('典型成分数据分布示例', fontsize=12, fontweight='bold')
ax3.set_xlabel('成分')
ax3.set_ylabel('10000*Wrec/(E²)')
ax3.set_xticks(range(1, 6))
ax3.set_xticklabels([f'成分{i}' for i in range(1, 6)])
ax3.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
ax3.grid(True, alpha=0.3)

# 4. 差异比率可视化
ax4 = axes[1, 1]
categories = ['不同成分间\n变异系数', '相同成分内\n平均变异系数']
values = [cv_between, avg_within_cv]
colors_bar = ['#FF6B6B', '#4ECDC4']

bars = ax4.bar(categories, values, color=colors_bar, alpha=0.8, edgecolor='black')
for bar, value in zip(bars, values):
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
             f'{value:.4f}', ha='center', va='bottom', fontweight='bold')

ax4.set_title(f'差异比率 = {cv_between:.4f} / {avg_within_cv:.4f} = {ratio:.3f}', 
             fontsize=12, fontweight='bold')
ax4.set_ylabel('变异系数')
ax4.grid(True, alpha=0.3)

# 添加比率说明
ax4.text(0.5, 0.8, f'比率 = {ratio:.3f} > 1\n成分是主要影响因素', 
         transform=ax4.transAxes, ha='center', va='center',
         bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7),
         fontsize=11, fontweight='bold')

plt.tight_layout()
plt.savefig('ratio_calculation_explanation.png', dpi=300, bbox_inches='tight')
plt.show()

print(f"\n4. 实际计算验证:")
print(f"   使用的数据: {len(df_analysis)} 个数据点，{len(components_with_multiple_tests)} 个成分")
print(f"   不同成分间变异系数: {cv_between:.6f}")
print(f"   相同成分内平均变异系数: {avg_within_cv:.6f}")
print(f"   差异比率: {ratio:.6f}")

print(f"\n5. 统计学意义:")
print(f"   - 差异比率量化了'成分效应'相对于'随机变异'的大小")
print(f"   - 比率越大，说明成分对性能的影响越显著")
print(f"   - 这是一个无量纲指标，便于不同研究间的比较")

print("\n计算过程可视化图表已保存为 ratio_calculation_explanation.png")
